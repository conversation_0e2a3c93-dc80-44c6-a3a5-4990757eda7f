#!/usr/bin/env python3
"""
Teste da execução principal
"""

import tkinter as tk

def test_main():
    try:
        print("Criando janela principal...")
        root = tk.Tk()
        root.title("Teste Principal")
        root.geometry("400x300")
        
        label = tk.Label(root, text="Teste executado com sucesso!")
        label.pack(pady=50)
        
        button = tk.Button(root, text="Fechar", command=root.quit)
        button.pack()
        
        print("Iniciando mainloop...")
        root.mainloop()
        print("Mainloop finalizado.")
        
    except Exception as e:
        print(f"Erro: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_main()
