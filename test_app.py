#!/usr/bin/env python3
"""
Teste simples da aplicação modernizada
"""

import tkinter as tk
from tkinter import ttk

def test_basic_window():
    """Testa se a janela básica funciona"""
    root = tk.Tk()
    root.title("Teste - Interface Moderna")
    root.geometry("400x300")
    
    label = tk.Label(root, text="Teste da Interface Moderna", font=('Segoe UI', 14))
    label.pack(pady=20)
    
    button = tk.Button(root, text="Fechar", command=root.quit)
    button.pack(pady=10)
    
    print("Janela de teste criada com sucesso!")
    root.mainloop()

if __name__ == "__main__":
    test_basic_window()
