import os
import sys
import datetime
import threading
import unicodedata
from typing import Any, Dict, List, Optional, Tuple
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext, ttk
from tkinter import font as tkFont

try:
    import pyodbc
except ImportError:
    # Só mostrar o erro se estivermos executando a aplicação principal
    if __name__ == "__main__":
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Ocultar janela principal
        messagebox.showerror("Erro de Dependência",
                           "A biblioteca 'pyodbc' não está instalada.\n\n"
                           "Execute no seu terminal:\npip install pyodbc")
        root.destroy()
    sys.exit(1)


def find_access_driver() -> Optional[str]:
    """Tenta encontrar um driver ODBC do Access instalado no Windows."""
    candidates = [
        "Microsoft Access Driver (*.mdb, *.accdb)",
        "Microsoft Access Driver (*.mdb)",
        "Microsoft Access Driver (*.accdb)",
    ]
    available = [d for d in pyodbc.drivers()]
    for cand in candidates:
        if cand in available:
            return cand
    return None


def connect_mdb(db_path: str) -> pyodbc.Connection:
    driver = find_access_driver()
    if not driver:
        print("Erro: Nenhum driver ODBC do Microsoft Access foi encontrado no sistema.", file=sys.stderr)
        print("Instale o 'Microsoft Access Database Engine' (32 ou 64 bits conforme seu Python).", file=sys.stderr)
        sys.exit(2)
    conn_str = f"DRIVER={{{driver}}};DBQ={db_path};READONLY=TRUE;"
    try:
        return pyodbc.connect(conn_str)
    except pyodbc.Error as e:
        print(f"Erro ao conectar ao MDB: {e}", file=sys.stderr)
        sys.exit(3)


def normalize_name(name: str) -> str:
    """Normaliza um nome para ser um identificador SQL seguro."""
    if not name:
        return ""
    # NFD normaliza caracteres acentuados em caractere base + acento
    nfkd_form = unicodedata.normalize('NFKD', name)
    # Remove os acentos (combinadores)
    sem_acentos = "".join([c for c in nfkd_form if not unicodedata.combining(c)])
    # Substitui espaços e converte para minúsculas
    return sem_acentos.replace(' ', '_').lower()


def quote_ident(name: str) -> str:
    """Normaliza e coloca aspas em um identificador."""
    normalized = normalize_name(name)
    return '"' + normalized.replace('"', '""') + '"'


def is_quantidade_field(name: str) -> bool:
    """Retorna True se o nome do campo contiver 'quantidade' (case-insensitive, normalizado)."""
    if not name:
        return False
    n = normalize_name(name)
    return "quantidade" in n


def is_hora_field(name: str) -> bool:
    """Retorna True se o nome do campo contiver 'hora' ou 'horario' (case-insensitive, normalizado)."""
    if not name:
        return False
    n = normalize_name(name)
    return "hora" in n or "horario" in n


# Mapear tipos do Access/ODBC para PostgreSQL
ACCESS_TO_PG_TYPE: Dict[str, str] = {
    # Texto
    "VARCHAR": "varchar(100)",
    "LONGCHAR": "text",
    "TEXT": "text",
    "MEMO": "text",
    # Inteiros
    "BYTE": "numeric(10,2)",
    "SHORT": "numeric(10,2)",
    "INTEGER": "integer",
    "LONG": "numeric(10,2)",
    "COUNTER": "numeric(10,2)",  # autonumber; não marcamos como serial para manter portabilidade
    # Numéricos
    "NUMERIC": "numeric(10,2)",
    "DECIMAL": "numeric(10,2)",
    "CURRENCY": "numeric(10,2)",
    "DOUBLE": "numeric(10,2)",
    "SINGLE": "real",
    # Booleano
    "YESNO": "boolean",
    "BIT": "boolean",
    # Datas
    "DATETIME": "date",
    "DATE": "date",
    "TIME": "time",
    # Binário
    "BINARY": "bytea",
    "LONGBINARY": "bytea",
    "OLEOBJECT": "bytea",
    # GUID
    "GUID": "uuid",
}


def odbc_type_to_pg(type_name: Optional[str], column_size: Optional[int], decimal_digits: Optional[int]) -> str:
    t = (type_name or "").upper()
    base = ACCESS_TO_PG_TYPE.get(t)
    if base is None:
        # fallback genérico por família de tipo
        if t in {"CHAR", "NCHAR", "NVARCHAR"}:
            base = "varchar(100)"
        elif t in {"FLOAT", "DOUBLE", "REAL"}:
            base = "double precision" if t != "REAL" else "real"
        elif t in {"TINYINT", "SMALLINT"}:
            base = "numeric(10)" 
        elif t in {"INT", "INTEGER", "LONG"}:
            base = "integer" 
        elif t in {"BIGINT"}:
            base = "bigint"
        elif t in {"DECIMAL", "NUMERIC"}:
            base = "numeric"
        elif t in {"BIT", "BOOLEAN", "YESNO"}:
            base = "boolean"
        elif t in {"DATETIME", "TIMESTAMP", "DATE", "TIME"}:
            base = "datetime"
        else:
            base = "varchar(100)"  # último recurso
    return base


def get_tables(cursor: pyodbc.Cursor) -> List[str]:
    tables: List[str] = []
    for row in cursor.tables(tableType='TABLE'):
        name = row.table_name
        if not name:
            continue
        if name.startswith("MSys"):
            continue  # ignora tabelas de sistema do Access
        tables.append(name)
    return tables


def get_columns(cursor: pyodbc.Cursor, table: str) -> List[Dict[str, Any]]:
    cols: List[Dict[str, Any]] = []
    for col in cursor.columns(table=table):
        cols.append({
            "name": col.column_name,
            "type_name": getattr(col, 'type_name', None),
            "data_type": getattr(col, 'data_type', None),  # código ODBC (não usamos diretamente)
            "column_size": getattr(col, 'column_size', None),
            "decimal_digits": getattr(col, 'decimal_digits', None),
            "nullable": bool(getattr(col, 'nullable', True)),
            "remarks": getattr(col, 'remarks', None),
            "ordinal_position": getattr(col, 'ordinal_position', None),
        })
    # Ordena pela posição ordinal se disponível
    if cols and any(c.get('ordinal_position') is not None for c in cols):
        cols.sort(key=lambda c: (c.get('ordinal_position') is None, c.get('ordinal_position', 0)))
    return cols


def get_primary_keys(cursor: pyodbc.Cursor, table: str) -> List[str]:
    pks: List[str] = []
    try:
        for pk in cursor.primaryKeys(table=table):
            if pk.column_name:
                pks.append(pk.column_name)
    except Exception:
        pass
    return pks


def format_value(value: Any) -> str:
    if value is None:
        return "NULL"
    if isinstance(value, bool):
        return "TRUE" if value else "FALSE"
    if isinstance(value, (int, float)):
        # Cobre numeric simples; cuidado com NaN/inf
        if isinstance(value, float):
            if value != value:  # NaN
                return "NULL"
            if value == float('inf') or value == float('-inf'):
                return "NULL"
        return str(value)
    if isinstance(value, (datetime.date, datetime.datetime)):
        # Representa como literal de timestamp ISO
        return "'" + value.strftime('%Y-%m-%d %H:%M:%S') + "'"
    if isinstance(value, (bytes, bytearray, memoryview)):
        # Bytea em hex: '\xDEADBEEF'
        hexstr = bytes(value).hex()
        return "'\\x" + hexstr + "'::bytea"
    # String e outros
    s = str(value)
    s = s.replace("'", "''")
    return "'" + s + "'"


def generate_create_table(table: str, columns: List[Dict[str, Any]], pks: List[str]) -> str:
    """Gera o comando CREATE TABLE, transformando o primeiro campo em serial PRIMARY KEY."""
    if not columns:
        return f"-- AVISO: Tabela '{normalize_name(table)}' sem colunas, CREATE TABLE não gerado.\n"

    parts: List[str] = []
    
    # Processa o primeiro campo como a chave primária serial
    first_col = columns[0]
    first_col_name = quote_ident(first_col["name"])
    parts.append(f"    {first_col_name} serial NOT NULL PRIMARY KEY")

    # Processa os campos restantes
    for col in columns[1:]:
        colname = quote_ident(col["name"])
        pgtype = odbc_type_to_pg(col.get("type_name"), col.get("column_size"), col.get("decimal_digits"))
        # Regras especiais para tipos baseados no nome do campo
        col_name = col.get("name", "")
        if is_quantidade_field(col_name):
            pgtype = "numeric(10,2)"
        elif is_hora_field(col_name):
            pgtype = "time"
        
        # Não especifica NULL ou NOT NULL, usando o padrão do banco
        parts.append(f"    {colname} {pgtype}")

    cols_sql = ",\n".join(parts)
    return f"CREATE TABLE {quote_ident(table)} (\n{cols_sql}\n);\n"


def generate_inserts(cursor: pyodbc.Cursor, table: str, columns: List[Dict[str, Any]]) -> List[str]:
    """Gera os comandos INSERT, ignorando a primeira coluna (que se tornou serial)."""
    if len(columns) < 2:
        # Não há dados para inserir se só havia uma coluna (que virou serial)
        return []

    # Ignora a primeira coluna para o INSERT e para o SELECT
    cols_for_insert = columns[1:]
    original_col_names = [c["name"] for c in cols_for_insert]
    normalized_col_names_sql = ", ".join(quote_ident(n) for n in original_col_names)
    
    # Usar SELECT * é mais robusto contra palavras-chave do Access como 'COUNT'.
    # A lógica de pular a primeira coluna será feita ao processar o resultado.
    query = f"SELECT * FROM [{table}]"

    inserts: List[str] = []
    try:
        cursor.execute(query)
    except pyodbc.Error as e:
        raise RuntimeError(f"Falha ao ler dados da tabela {table}: {e}")

    row = cursor.fetchone()
    while row is not None:
        # Pula o primeiro valor da linha, pois a primeira coluna virou serial
        values_sql = ", ".join(format_value(value) for value in row[1:])
        inserts.append(f"INSERT INTO {quote_ident(table)} ({normalized_col_names_sql}) VALUES ({values_sql});")
        row = cursor.fetchone()
    return inserts


class ModernTheme:
    """Sistema de design moderno para a aplicação"""

    # Cores principais
    PRIMARY = "#2563eb"      # Azul moderno
    PRIMARY_DARK = "#1d4ed8"
    PRIMARY_LIGHT = "#3b82f6"

    # Cores secundárias
    SUCCESS = "#10b981"      # Verde
    WARNING = "#f59e0b"      # Amarelo
    ERROR = "#ef4444"        # Vermelho
    INFO = "#06b6d4"         # Ciano

    # Cores neutras
    BACKGROUND = "#f8fafc"   # Cinza muito claro
    SURFACE = "#ffffff"      # Branco
    SURFACE_VARIANT = "#f1f5f9"  # Cinza claro

    # Texto
    TEXT_PRIMARY = "#0f172a"     # Quase preto
    TEXT_SECONDARY = "#64748b"   # Cinza médio
    TEXT_DISABLED = "#94a3b8"    # Cinza claro

    # Bordas
    BORDER = "#e2e8f0"       # Cinza claro
    BORDER_FOCUS = "#3b82f6" # Azul para foco

    # Sombras
    SHADOW_LIGHT = "#00000010"
    SHADOW_MEDIUM = "#00000020"

    @staticmethod
    def configure_style():
        """Configura o estilo ttk para aparência moderna"""
        style = ttk.Style()

        # Configurar tema base
        style.theme_use('clam')

        # Botões modernos
        style.configure('Modern.TButton',
                       background=ModernTheme.PRIMARY,
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 10))

        style.map('Modern.TButton',
                 background=[('active', ModernTheme.PRIMARY_DARK),
                           ('pressed', ModernTheme.PRIMARY_DARK)])

        # Botão de sucesso
        style.configure('Success.TButton',
                       background=ModernTheme.SUCCESS,
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(20, 12))

        style.map('Success.TButton',
                 background=[('active', '#059669'),
                           ('pressed', '#047857')])

        # Entry moderno
        style.configure('Modern.TEntry',
                       borderwidth=1,
                       relief='solid',
                       padding=(12, 8))

        # Frame com sombra
        style.configure('Card.TFrame',
                       background=ModernTheme.SURFACE,
                       relief='flat',
                       borderwidth=1)

        return style


class ModernButton(tk.Button):
    """Botão customizado com aparência moderna"""

    def __init__(self, parent, text="", command=None, style="primary", **kwargs):
        # Configurações padrão baseadas no estilo
        if style == "primary":
            bg = ModernTheme.PRIMARY
            hover_bg = ModernTheme.PRIMARY_DARK
            fg = "white"
        elif style == "success":
            bg = ModernTheme.SUCCESS
            hover_bg = "#059669"
            fg = "white"
        elif style == "secondary":
            bg = ModernTheme.SURFACE_VARIANT
            hover_bg = ModernTheme.BORDER
            fg = ModernTheme.TEXT_PRIMARY
        else:
            bg = ModernTheme.PRIMARY
            hover_bg = ModernTheme.PRIMARY_DARK
            fg = "white"

        # Configurações do botão
        default_config = {
            'text': text,
            'command': command,
            'bg': bg,
            'fg': fg,
            'activebackground': hover_bg,
            'activeforeground': fg,
            'relief': 'flat',
            'borderwidth': 0,
            'cursor': 'hand2',
            'font': ('Segoe UI', 10, 'normal'),
            'padx': 20,
            'pady': 10
        }

        # Mesclar com configurações personalizadas
        default_config.update(kwargs)

        super().__init__(parent, **default_config)

        # Efeitos de hover
        self.bind("<Enter>", lambda e: self.config(bg=hover_bg))
        self.bind("<Leave>", lambda e: self.config(bg=bg))


class ModernEntry(tk.Frame):
    """Entry customizado com label e aparência moderna"""

    def __init__(self, parent, label="", placeholder="", **kwargs):
        super().__init__(parent, bg=ModernTheme.BACKGROUND)

        # Label
        if label:
            self.label = tk.Label(self, text=label,
                                bg=ModernTheme.BACKGROUND,
                                fg=ModernTheme.TEXT_PRIMARY,
                                font=('Segoe UI', 9, 'normal'))
            self.label.pack(anchor='w', pady=(0, 5))

        # Entry
        self.entry = tk.Entry(self,
                            bg=ModernTheme.SURFACE,
                            fg=ModernTheme.TEXT_PRIMARY,
                            relief='solid',
                            borderwidth=1,
                            highlightthickness=0,
                            font=('Segoe UI', 10),
                            **kwargs)
        self.entry.pack(fill='x', ipady=8, ipadx=12)

        # Placeholder
        if placeholder:
            self.placeholder = placeholder
            self.entry.insert(0, placeholder)
            self.entry.config(fg=ModernTheme.TEXT_DISABLED)
            self.entry.bind('<FocusIn>', self._on_focus_in)
            self.entry.bind('<FocusOut>', self._on_focus_out)

        # Efeitos de foco
        self.entry.bind('<FocusIn>', self._on_entry_focus_in)
        self.entry.bind('<FocusOut>', self._on_entry_focus_out)

    def _on_focus_in(self, event):
        if self.entry.get() == self.placeholder:
            self.entry.delete(0, tk.END)
            self.entry.config(fg=ModernTheme.TEXT_PRIMARY)

    def _on_focus_out(self, event):
        if not self.entry.get():
            self.entry.insert(0, self.placeholder)
            self.entry.config(fg=ModernTheme.TEXT_DISABLED)

    def _on_entry_focus_in(self, event):
        self.entry.config(highlightbackground=ModernTheme.BORDER_FOCUS,
                         highlightcolor=ModernTheme.BORDER_FOCUS,
                         highlightthickness=2)

    def _on_entry_focus_out(self, event):
        self.entry.config(highlightthickness=0)

    def get(self):
        value = self.entry.get()
        if hasattr(self, 'placeholder') and value == self.placeholder:
            return ""
        return value

    def set(self, value):
        self.entry.delete(0, tk.END)
        self.entry.insert(0, value)
        if hasattr(self, 'placeholder'):
            self.entry.config(fg=ModernTheme.TEXT_PRIMARY)


class ConverterApp:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_variables()
        self.setup_ui()

    def setup_window(self):
        """Configura a janela principal"""
        self.root.title("Conversor MDB → PostgreSQL")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        self.root.configure(bg=ModernTheme.BACKGROUND)

        # Configurar estilo moderno
        ModernTheme.configure_style()

        # Centralizar janela
        self.center_window()

        # Configurar fonte padrão
        default_font = tkFont.nametofont("TkDefaultFont")
        default_font.configure(family="Segoe UI", size=9)

    def center_window(self):
        """Centraliza a janela na tela"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def setup_variables(self):
        """Inicializa as variáveis"""
        self.input_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.is_converting = False

    def setup_ui(self):
        """Configura a interface do usuário"""
        # Container principal com padding
        main_container = tk.Frame(self.root, bg=ModernTheme.BACKGROUND)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Header
        self.create_header(main_container)

        # Card principal
        self.create_main_card(main_container)

        # Footer com informações
        self.create_footer(main_container)

    def create_header(self, parent):
        """Cria o cabeçalho da aplicação"""
        header_frame = tk.Frame(parent, bg=ModernTheme.BACKGROUND)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Título principal
        title_label = tk.Label(header_frame,
                              text="Conversor MDB → PostgreSQL",
                              font=('Segoe UI', 24, 'bold'),
                              fg=ModernTheme.TEXT_PRIMARY,
                              bg=ModernTheme.BACKGROUND)
        title_label.pack(anchor=tk.W)

        # Subtítulo
        subtitle_label = tk.Label(header_frame,
                                 text="Converta bancos de dados Microsoft Access para PostgreSQL",
                                 font=('Segoe UI', 11),
                                 fg=ModernTheme.TEXT_SECONDARY,
                                 bg=ModernTheme.BACKGROUND)
        subtitle_label.pack(anchor=tk.W, pady=(5, 0))

    def create_main_card(self, parent):
        """Cria o card principal com os controles"""
        # Card container
        card_frame = tk.Frame(parent,
                             bg=ModernTheme.SURFACE,
                             relief='flat',
                             bd=1)
        card_frame.pack(fill=tk.BOTH, expand=True)

        # Adicionar sombra visual
        shadow_frame = tk.Frame(parent, bg=ModernTheme.SHADOW_LIGHT, height=2)
        shadow_frame.place(in_=card_frame, relx=0.02, rely=0.02, relwidth=0.98, relheight=0.98)

        # Conteúdo do card
        content_frame = tk.Frame(card_frame, bg=ModernTheme.SURFACE)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # Seção de arquivos
        self.create_file_section(content_frame)

        # Botão de conversão
        self.create_conversion_button(content_frame)

        # Seção de progresso
        self.create_progress_section(content_frame)

        # Log
        self.create_log_section(content_frame)

    def create_file_section(self, parent):
        """Cria a seção de seleção de arquivos"""
        files_frame = tk.Frame(parent, bg=ModernTheme.SURFACE)
        files_frame.pack(fill=tk.X, pady=(0, 30))

        # Título da seção
        section_title = tk.Label(files_frame,
                               text="📁 Seleção de Arquivos",
                               font=('Segoe UI', 14, 'bold'),
                               fg=ModernTheme.TEXT_PRIMARY,
                               bg=ModernTheme.SURFACE)
        section_title.pack(anchor=tk.W, pady=(0, 20))

        # Arquivo de entrada
        input_frame = tk.Frame(files_frame, bg=ModernTheme.SURFACE)
        input_frame.pack(fill=tk.X, pady=(0, 15))

        self.input_entry = ModernEntry(input_frame,
                                      label="Arquivo de Entrada (MDB/ACCDB)",
                                      placeholder="Selecione o arquivo de banco de dados...")
        self.input_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        input_btn = ModernButton(input_frame,
                               text="📂 Procurar",
                               command=self.select_input_file,
                               style="secondary")
        input_btn.pack(side=tk.RIGHT, padx=(15, 0))

        # Arquivo de saída
        output_frame = tk.Frame(files_frame, bg=ModernTheme.SURFACE)
        output_frame.pack(fill=tk.X)

        self.output_entry = ModernEntry(output_frame,
                                       label="Arquivo de Saída (.sql)",
                                       placeholder="Escolha onde salvar o arquivo SQL...")
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        output_btn = ModernButton(output_frame,
                                text="💾 Salvar como",
                                command=self.select_output_file,
                                style="secondary")
        output_btn.pack(side=tk.RIGHT, padx=(15, 0))

    def create_conversion_button(self, parent):
        """Cria o botão de conversão"""
        button_frame = tk.Frame(parent, bg=ModernTheme.SURFACE)
        button_frame.pack(fill=tk.X, pady=(0, 30))

        self.convert_button = ModernButton(button_frame,
                                         text="🚀 Iniciar Conversão",
                                         command=self.start_conversion,
                                         style="success")
        self.convert_button.pack(anchor=tk.CENTER)

    def create_progress_section(self, parent):
        """Cria a seção de progresso"""
        self.progress_frame = tk.Frame(parent, bg=ModernTheme.SURFACE)
        self.progress_frame.pack(fill=tk.X, pady=(0, 20))

        # Inicialmente oculto
        self.progress_frame.pack_forget()

        progress_label = tk.Label(self.progress_frame,
                                text="Progresso da Conversão",
                                font=('Segoe UI', 12, 'bold'),
                                fg=ModernTheme.TEXT_PRIMARY,
                                bg=ModernTheme.SURFACE)
        progress_label.pack(anchor=tk.W, pady=(0, 10))

        # Barra de progresso
        self.progress_bar = ttk.Progressbar(self.progress_frame,
                                          mode='indeterminate',
                                          length=400)
        self.progress_bar.pack(anchor=tk.W)

        # Status
        self.status_label = tk.Label(self.progress_frame,
                                   text="Preparando conversão...",
                                   font=('Segoe UI', 9),
                                   fg=ModernTheme.TEXT_SECONDARY,
                                   bg=ModernTheme.SURFACE)
        self.status_label.pack(anchor=tk.W, pady=(5, 0))

    def create_log_section(self, parent):
        """Cria a seção de log"""
        log_frame = tk.Frame(parent, bg=ModernTheme.SURFACE)
        log_frame.pack(fill=tk.BOTH, expand=True)

        # Título do log
        log_title = tk.Label(log_frame,
                           text="📋 Log da Conversão",
                           font=('Segoe UI', 12, 'bold'),
                           fg=ModernTheme.TEXT_PRIMARY,
                           bg=ModernTheme.SURFACE)
        log_title.pack(anchor=tk.W, pady=(0, 10))

        # Text widget para log
        self.log_text = scrolledtext.ScrolledText(log_frame,
                                                wrap=tk.WORD,
                                                height=12,
                                                bg=ModernTheme.SURFACE_VARIANT,
                                                fg=ModernTheme.TEXT_PRIMARY,
                                                font=('Consolas', 9),
                                                relief='solid',
                                                borderwidth=1,
                                                padx=15,
                                                pady=10)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.configure(state='disabled')

    def create_footer(self, parent):
        """Cria o rodapé com informações"""
        footer_frame = tk.Frame(parent, bg=ModernTheme.BACKGROUND)
        footer_frame.pack(fill=tk.X, pady=(20, 0))

        footer_text = tk.Label(footer_frame,
                             text="💡 Dica: Certifique-se de que o Microsoft Access Database Engine esteja instalado",
                             font=('Segoe UI', 9),
                             fg=ModernTheme.TEXT_SECONDARY,
                             bg=ModernTheme.BACKGROUND)
        footer_text.pack(anchor=tk.W)

    def log(self, message, level="info"):
        """Adiciona mensagem ao log com timestamp e nível"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # Emojis para diferentes níveis
        level_icons = {
            "info": "ℹ️",
            "success": "✅",
            "warning": "⚠️",
            "error": "❌",
            "process": "⚙️"
        }

        icon = level_icons.get(level, "ℹ️")
        formatted_message = f"[{timestamp}] {icon} {message}"

        self.root.after(0, self._log_thread_safe, formatted_message)

    def _log_thread_safe(self, message):
        """Thread-safe logging"""
        self.log_text.configure(state='normal')
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.configure(state='disabled')

    def update_status(self, message):
        """Atualiza o status na barra de progresso"""
        if hasattr(self, 'status_label'):
            self.root.after(0, lambda: self.status_label.config(text=message))

    def select_input_file(self):
        """Seleciona arquivo de entrada com validação"""
        path = filedialog.askopenfilename(
            title="Selecione o arquivo de banco de dados Access",
            filetypes=(
                ("Bancos de Dados Access", "*.mdb *.accdb"),
                ("Access 2003 e anterior", "*.mdb"),
                ("Access 2007 e posterior", "*.accdb"),
                ("Todos os arquivos", "*.*")
            ),
            initialdir=os.path.expanduser("~")
        )

        if path:
            # Validar se o arquivo existe e é acessível
            if not os.path.exists(path):
                messagebox.showerror("Erro", "O arquivo selecionado não existe.")
                return

            if not os.access(path, os.R_OK):
                messagebox.showerror("Erro", "Não é possível ler o arquivo selecionado.")
                return

            self.input_entry.set(path)

            # Sugerir nome de arquivo de saída
            base_name = os.path.splitext(os.path.basename(path))[0]
            suggested_output = os.path.join(os.path.dirname(path), f"{base_name}_postgres.sql")
            self.output_entry.set(suggested_output)

            self.log(f"Arquivo de entrada selecionado: {os.path.basename(path)}", "info")

    def select_output_file(self):
        """Seleciona arquivo de saída"""
        # Usar diretório do arquivo de entrada como padrão
        initial_dir = os.path.expanduser("~")
        input_file = self.input_entry.get()
        if input_file and os.path.exists(input_file):
            initial_dir = os.path.dirname(input_file)

        path = filedialog.asksaveasfilename(
            title="Salvar arquivo SQL como...",
            defaultextension=".sql",
            filetypes=(
                ("Arquivos SQL", "*.sql"),
                ("Todos os arquivos", "*.*")
            ),
            initialdir=initial_dir
        )

        if path:
            # Garantir extensão .sql
            if not path.lower().endswith('.sql'):
                path += '.sql'

            self.output_entry.set(path)
            self.log(f"Arquivo de saída definido: {os.path.basename(path)}", "info")

    def start_conversion(self):
        """Inicia o processo de conversão com validações"""
        input_file = self.input_entry.get()
        output_file = self.output_entry.get()

        # Validações
        if not input_file or not output_file:
            messagebox.showerror("Campos Obrigatórios",
                               "Por favor, selecione os arquivos de entrada e saída.")
            return

        if not os.path.exists(input_file):
            messagebox.showerror("Arquivo Não Encontrado",
                               "O arquivo de entrada não foi encontrado.")
            return

        # Verificar se o arquivo de saída pode ser criado
        output_dir = os.path.dirname(output_file)
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                messagebox.showerror("Erro de Permissão",
                                   f"Não é possível criar o diretório de saída:\n{e}")
                return

        # Preparar interface para conversão
        self.is_converting = True
        self.convert_button.config(text="🔄 Convertendo...", state=tk.DISABLED)

        # Limpar log
        self.log_text.configure(state='normal')
        self.log_text.delete('1.0', tk.END)
        self.log_text.configure(state='disabled')

        # Mostrar barra de progresso
        self.progress_frame.pack(fill=tk.X, pady=(0, 20))
        self.progress_bar.start(10)
        self.update_status("Iniciando conversão...")

        # Executar conversão em thread separada
        thread = threading.Thread(target=self.run_conversion, args=(input_file, output_file))
        thread.daemon = True
        thread.start()

    def run_conversion(self, db_path, output_path):
        """Executa a conversão do banco de dados"""
        try:
            self.log(f"Iniciando conversão de '{os.path.basename(db_path)}'", "process")
            self.update_status("Conectando ao banco de dados...")

            conn = connect_mdb(db_path)
            cur = conn.cursor()

            self.update_status("Analisando estrutura do banco...")
            self.log("Buscando tabelas no banco de dados...", "process")

            tables = get_tables(cur)
            if not tables:
                self.log("Nenhuma tabela de usuário encontrada no banco.", "warning")
                self.update_status("Nenhuma tabela encontrada")
                return

            self.log(f"Encontradas {len(tables)} tabelas: {', '.join(tables)}", "success")

            # Preparar cabeçalho do SQL
            statements: List[str] = []
            statements.append("-- =====================================================")
            statements.append("-- SQL gerado a partir do Microsoft Access para PostgreSQL")
            statements.append(f"-- Arquivo fonte: {os.path.basename(db_path)}")
            statements.append(f"-- Gerado em: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            statements.append("-- =====================================================")
            statements.append("")
            statements.append("SET standard_conforming_strings = on;")
            statements.append("SET client_encoding = 'UTF8';")
            statements.append("")
            statements.append("-- ================= ESTRUTURA DAS TABELAS =================")

            # Processar estrutura das tabelas
            total_tables = len(tables)
            for i, t in enumerate(tables, 1):
                self.update_status(f"Processando estrutura da tabela '{t}' ({i}/{total_tables})...")
                self.log(f"Processando schema da tabela '{t}' ({i}/{total_tables})", "process")

                cols = get_columns(cur, t)
                if not cols:
                    self.log(f"Tabela '{t}' sem colunas detectadas (pode ser vinculada)", "warning")
                    continue

                pks = get_primary_keys(cur, t)
                table_sql = generate_create_table(t, cols, pks)
                statements.append(f"\n-- Tabela: {t}")
                statements.append(table_sql)

                self.log(f"Estrutura da tabela '{t}' processada ({len(cols)} colunas)", "success")

            statements.append("\n-- ================= DADOS DAS TABELAS =================")

            # Processar dados das tabelas
            total_records = 0
            for i, t in enumerate(tables, 1):
                self.update_status(f"Exportando dados da tabela '{t}' ({i}/{total_tables})...")
                self.log(f"Exportando dados da tabela '{t}' ({i}/{total_tables})", "process")

                cols = get_columns(cur, t)
                if not cols:
                    continue

                try:
                    inserts = generate_inserts(cur, t, cols)
                    if inserts:
                        statements.append(f"\n-- Dados da tabela: {quote_ident(t)}")
                        statements.extend(inserts)
                        total_records += len(inserts)
                        self.log(f"Tabela '{t}': {len(inserts)} registros exportados", "success")
                    else:
                        self.log(f"Tabela '{t}': nenhum registro encontrado", "info")
                except Exception as e:
                    self.log(f"Erro ao exportar dados da tabela '{t}': {e}", "error")

            # Salvar arquivo
            self.update_status("Salvando arquivo SQL...")
            self.log("Gerando arquivo SQL final...", "process")

            sql_text = "\n".join(statements) + "\n"

            with open(output_path, "w", encoding="utf-8") as f:
                f.write(sql_text)

            # Sucesso
            self.update_status("Conversão concluída com sucesso!")
            self.log("", "info")  # Linha em branco
            self.log("🎉 CONVERSÃO CONCLUÍDA COM SUCESSO!", "success")
            self.log(f"📊 Estatísticas:", "info")
            self.log(f"   • Tabelas processadas: {len(tables)}", "info")
            self.log(f"   • Total de registros: {total_records}", "info")
            self.log(f"   • Arquivo gerado: {os.path.basename(output_path)}", "info")
            self.log(f"   • Tamanho do arquivo: {os.path.getsize(output_path):,} bytes", "info")

            # Mostrar dialog de sucesso
            result = messagebox.askyesno("Conversão Concluída!",
                                       f"Conversão realizada com sucesso!\n\n"
                                       f"📊 Estatísticas:\n"
                                       f"• {len(tables)} tabelas processadas\n"
                                       f"• {total_records:,} registros exportados\n"
                                       f"• Arquivo: {os.path.basename(output_path)}\n\n"
                                       f"Deseja abrir a pasta do arquivo?")

            if result:
                # Abrir pasta do arquivo
                if os.name == 'nt':  # Windows
                    os.startfile(os.path.dirname(output_path))
                elif os.name == 'posix':  # Linux/Mac
                    os.system(f'xdg-open "{os.path.dirname(output_path)}"')

        except Exception as e:
            self.update_status("Erro na conversão")
            self.log(f"ERRO DURANTE A CONVERSÃO: {e}", "error")
            messagebox.showerror("Erro na Conversão",
                               f"Ocorreu um erro durante a conversão:\n\n{e}\n\n"
                               f"Verifique o log para mais detalhes.")
        finally:
            self.root.after(0, self._conversion_done)

    def _conversion_done(self):
        """Finaliza o processo de conversão e restaura a interface"""
        self.is_converting = False
        self.convert_button.config(state=tk.NORMAL, text="🚀 Iniciar Conversão")

        # Parar e ocultar barra de progresso
        self.progress_bar.stop()
        self.progress_frame.pack_forget()

        self.update_status("Pronto para nova conversão")

if __name__ == "__main__":
    root = tk.Tk()
    app = ConverterApp(root)
    root.mainloop()
